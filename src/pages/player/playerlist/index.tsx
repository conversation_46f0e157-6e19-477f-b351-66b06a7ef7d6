import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useContext } from 'react';
import { createContext } from 'react';

import { editPlayerStatus, editPlayerVipModify, getPlayerList } from '@/api/player';
import { getVipList } from '@/api/vip';
import DownloadIcon from '@/assets/img/icon/download.svg?react';
import SystemIcon from '@/assets/img/icon/system.svg?react';
import CopyIcon from '@/components/CopyIcon';
import QuickDateSelect from '@/components/QuickDateSelect';
import RButton from '@/components/RButton';
import ButtonDropdown from '@/components/RDropdown';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RMultipleSelect from '@/components/RMultipleSelect';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import { RTabs } from '@/components/RTabs';
import RTag from '@/components/RTag';
import { tagColors } from '@/components/RTag/types';
import RTooltip from '@/components/Rtooltip';
import SearchForm from '@/components/SearchForm';
import StatusLabel from '@/components/StatusLabel';
import StatusSelect from '@/components/StatusSelect';
import TagSelect from '@/components/TagSelect';
import { useAccountDialog } from '@/hooks/useAccountDialog';
import useConfirmModal from '@/hooks/useConfirmModal';
import { useLanguageIcon } from '@/hooks/useLanguageIcon';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import GetPlayerByPhoneModel from '@/pages/player/playerlist/playerByphoneModel';
import { PlayerData } from '@/types/playerlist';
import { Vip } from '@/types/vip';
import { formatTime } from '@/utils/time';

import VipChangeLogModal from './getVipRecordModel';
import PlayerPermissionModal from './permissionModel';
import PlayerModal, { FormValue as PlayerFormValue } from './playerModel';

type SearchFormValues = {
  timeStart?: number;
  timeEnd?: number;
  start?: number;
  end?: number;
  conditionType?: string;
  conditionValue?: string;
  registerWay?: string;
  tags?: { id: number; name: string }[];
  level?: number;
  status?: number;
};

const conditionOptions = [
  { label: 'pages_player_account', value: 'account' },
  { label: 'pages_player_nicknameSearch', value: 'name' },
  { label: 'pages_player_parentAccountSearch', value: 'refAccount' },
  { label: 'pages_player_phoneSearch', value: 'phone' }
];

const registerWayOptions = [
  { label: 'pages_player_registerWay_line', value: 3 },
  { label: 'pages_player_registerWay_backend', value: 1 },
  { label: 'pages_player_registerWay_phoneRegister', value: 2 }
];

const SearchParamsContext = createContext<SearchFormValues>({});

const ConditionSearch = () => {
  const { t } = useTranslation();
  return (
    <RForm.Item label={t('common_condition')}>
      <RForm.Item name="conditionType" className="inline-block !mr-2" initialValue="account">
        <RSelect
          options={conditionOptions.map((option) => ({
            label: t(option.label),
            value: option.value
          }))}
        />
      </RForm.Item>
      <RForm.Item name="conditionValue" className="inline-block">
        <RInput className="max-h-[32px]" />
      </RForm.Item>
    </RForm.Item>
  );
};

const RegisterWaySearch = () => {
  const { t } = useTranslation();
  const registerWayAllOption = registerWayOptions.map((option) => ({
    label: t(option.label),
    value: option.value
  }));
  return (
    <RForm.Item label={t('pages_player_registerWay')}>
      <RForm.Item name="registerType" className="inline-block !mr-2">
        <RMultipleSelect options={registerWayAllOption} onChange={() => {}} />
      </RForm.Item>
    </RForm.Item>
  );
};

const VipSelect = () => {
  const { t } = useTranslation();
  const { data: vipList } = useQuery({
    queryKey: ['vipList'],
    queryFn: () => getVipList({})
  });

  const vipListOptions =
    vipList?.data
      ?.filter((vip: Vip) => vip.status === 1)
      .map((vip: Vip) => ({ label: vip.name, value: vip.id })) || [];

  return (
    <RForm.Item name="level" label={t('pages_player_vipLevel')}>
      <RSelect options={vipListOptions} />
    </RForm.Item>
  );
};

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: SearchFormValues) => void;
  onReset: () => void;
}) => {
  const { t } = useTranslation();

  const handleSearch = (values: SearchFormValues) => {
    const { conditionType, conditionValue, ...rest } = values;
    if (conditionType && conditionValue) {
      const searchValues = {
        ...rest,
        [conditionType]: conditionValue
      };
      onSearch(searchValues);
    } else {
      onSearch(rest);
    }
  };

  return (
    <SearchForm<SearchFormValues> onSearch={handleSearch} onReset={onReset} className="">
      <RForm.Item name="date" label={t('pages_admin_registerTimeFilter')}>
        <QuickDateSelect />
      </RForm.Item>
      <ConditionSearch />
      <RegisterWaySearch />
      <TagSelect />
      <VipSelect />
      <RForm.Item name="status" label={t('common_status')} initialValue={undefined}>
        <StatusSelect />
      </RForm.Item>
    </SearchForm>
  );
};

const ActionButtons = ({
  data,
  onEdit,
  onEditStatus,
  isEditStatusPending,
  onOpenPermission
}: {
  data: PlayerData;
  onEdit: (data: PlayerData) => void;
  onEditStatus: (data: PlayerData) => void;
  isEditStatusPending: boolean;
  onOpenPermission: () => void;
}) => {
  const { t } = useTranslation();
  return (
    <div className="flex gap-1">
      <RButton
        size="small"
        variant="outlined"
        color="primary"
        type="link"
        onClick={() => onEdit(data)}
      >
        {t('common_edit')}
      </RButton>
      <RButton
        size="small"
        variant="outlined"
        color="primary"
        type="link"
        onClick={onOpenPermission}
      >
        {t('common_permission')}
      </RButton>
      {/*<RButton
        size="small"
        variant="outlined"
        color="cyan"
        type="link"
        onClick={() => console.log('詳情')}
      >
        {t('common_detail')}
      </RButton> */}
      <RButton
        size="small"
        variant="outlined"
        color={data.status === 1 ? 'red' : 'green'}
        type="link"
        loading={isEditStatusPending}
        onClick={() => onEditStatus(data)}
      >
        {data.status === 1 ? t('common_inactive') : t('common_active')}
      </RButton>
    </div>
  );
};

const getBaseColumns = (t: (key: string) => string) => [
  {
    title: 'common_account',
    dataIndex: 'account',
    width: 50,
    render: (account: string) => {
      return (
        <div className="flex items-center gap-1">
          <CopyIcon text={account} width={12} height={12} />
          <span className="truncate">{account}</span>
        </div>
      );
    }
  },
  {
    title: 'pages_player_nickname',
    dataIndex: 'name',
    width: 50,
    render: (name: string) => {
      return (
        <div className="flex items-center gap-1">
          <CopyIcon text={name} width={12} height={12} />
          <span className="truncate">{name}</span>
        </div>
      );
    }
  },
  {
    title: 'pages_player_vipLevel',
    dataIndex: 'level'
  },
  {
    title: 'pages_player_phone',
    dataIndex: 'phone'
  },
  {
    title: 'pages_player_registerWay',
    dataIndex: 'registerType',
    render: (registerType: number) => {
      return (
        <span>
          {registerType === 1
            ? t('pages_player_backend_label')
            : registerType === 2
              ? t('pages_player_phone_label')
              : t('pages_player_registerWay_line')}
        </span>
      );
    }
  },
  {
    title: 'pages_player_mainPointRemain',
    dataIndex: 'remain',
    render: (remain: number) => {
      const num = Number(remain);
      return <span>{isNaN(num) ? '-' : num.toFixed(0)}</span>;
    }
  },
  {
    title: 'pages_player_playerTag',
    dataIndex: 'tags',
    render: (tags: { id: number; name: string }[]) => {
      if (!tags || tags.length === 0) {
        return <RTag color="default">{t('pages_playerTag_Tag')}</RTag>;
      }
      return (
        <div className="flex flex-wrap gap-1">
          {tags.map((tag, index) => (
            <RTag key={tag.id} color={tagColors[index % tagColors.length]}>
              {tag.name}
            </RTag>
          ))}
        </div>
      );
    }
  },
  {
    title: 'pages_player_registerIp',
    dataIndex: 'registerIp',
    width: '15%',
    render: (registerIp: string, record: PlayerData) => {
      return (
        <div>
          <div className="mb-0.5">
            {registerIp}
            {record.registerIpIsoCode ? `(${record.registerIpIsoCode}) /` : '(-) /'}
          </div>
          {record.createdAt ? <div>{formatTime(record.createdAt)}</div> : '(-)'}
        </div>
      );
    }
  },
  {
    title: 'pages_player_lastLoginIp',
    dataIndex: 'lastLoginIp',
    width: '15%',
    render: (lastLoginIp: string, record: PlayerData) => {
      return (
        <div>
          <div>
            {lastLoginIp}
            {record.lastLoginIpIsoCode ? `(${record.lastLoginIpIsoCode}) /` : '(-) /'}
          </div>
          <div>{record.lastLoginTime ? formatTime(record.lastLoginTime) : '(-)'}</div>
        </div>
      );
    }
  },
  {
    title: 'common_status',
    dataIndex: 'status',
    width: '8%',
    render: (status: 1 | 0) => <StatusLabel status={status} />
  }
];

const PlayerListTable = ({ accountType }: { accountType: number }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [open, setOpen] = useState(false);
  const [openGetPlayerByPhone, setOpenGetPlayerByPhone] = useState(false);
  const [openPermission, setOpenPermission] = useState(false);
  const [openVipChangeLog, setOpenVipChangeLog] = useState(false);
  const [selectedPhone, setSelectedPhone] = useState<string>();
  const [selectedPhoneCountryCode, setSelectedPhoneCountryCode] = useState<string>();
  const [selectedRecord, setSelectedRecord] = useState<PlayerData | null>(null);
  const [initialValues, setInitialValues] = useState<PlayerFormValue>();
  const { confirmModal } = useConfirmModal();
  const { getLanguageIcon } = useLanguageIcon();
  const { showAccountDialog, AccountDialog } = useAccountDialog();

  const searchParams = useContext(SearchParamsContext);

  const { data, isPending } = useQuery({
    queryKey: ['playerList', { accountType, page, limit, ...searchParams }],
    queryFn: () => getPlayerList({ accountType, page, limit, ...searchParams })
  });

  const { mutate: editStatusMutate, isPending: isEditStatusPending } = useMutation({
    mutationFn: editPlayerStatus,
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['playerList'] })
  });

  const { mutate: editVipModifyMutate } = useMutation({
    mutationFn: editPlayerVipModify,
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['playerList'] })
  });

  const handleCloseModal = () => {
    setOpen(false);
    setTimeout(() => setInitialValues(undefined), 200);
  };

  const handleEdit = (data: PlayerData) => {
    setInitialValues({
      id: data.id,
      accountType: data.accountType,
      refAccount: data.refAccount,
      account: data.account,
      name: data.name,
      level: data.level,
      countryCode: data.countryCode,
      phone: data.phone,
      tags: data.tags.map(({ id }) => id),
      status: data.status
    });
    setOpen(true);
  };

  const handleEditStatus = (data: PlayerData) => {
    editStatusMutate({ id: data.id, status: data.status === 1 ? 0 : 1 });
  };

  const openEditStatusModal = (data: PlayerData) => {
    confirmModal({
      content: t(
        data.status === 1
          ? 'pages_admin_confirm_status_disable'
          : 'pages_admin_confirm_status_enable'
      ),
      onOk: () => handleEditStatus(data)
    });
  };

  const handleOpenPermission = (data: PlayerData) => {
    setSelectedRecord(data);
    setOpenPermission(true);
  };

  const handleOpenVipChangeLog = (data: PlayerData) => {
    setSelectedRecord(data);
    setOpenVipChangeLog(true);
  };

  const renderPhoneColumn = (phone: string, record: PlayerData) => (
    <div className="flex items-center gap-1">
      {getLanguageIcon(record.countryCode)}
      {phone ? (
        <a
          className="cursor-pointer !underline"
          onClick={() => {
            setSelectedPhone(phone);
            setSelectedPhoneCountryCode(record.countryCode);
            setOpenGetPlayerByPhone(true);
          }}
        >
          {record.countryCode}
          {phone}
        </a>
      ) : (
        <span className="text-gray-400">-</span>
      )}
    </div>
  );

  const renderVipLevelColumn = (level: number, record: PlayerData) => (
    <div className="flex items-center gap-1">
      <a className="cursor-pointer !underline" onClick={() => handleOpenVipChangeLog(record)}>
        <span>VIP{level}</span>
      </a>
      {record.isModify === 1 && (
        <ButtonDropdown
          items={[
            {
              key: 'vipRecord',
              label: t('pages_player_vipRecord'),
              onClick: () => handleOpenVipChangeLog(record)
            },
            {
              key: 'modifyStatus',
              label: t('pages_player_vipModify'),
              onClick: () =>
                confirmModal({
                  content: t('pages_player_vipModify_description'),
                  onOk: () => editVipModifyMutate({ id: record.id })
                })
            }
          ]}
          placement="bottomLeft"
          icon={<SystemIcon width={14} height={14} fill="var(--color-text-placeholder)" />}
          buttonProps={{ size: 'small', type: 'text' }}
        />
      )}
    </div>
  );

  const tableColumns = [
    ...getBaseColumns(t).map((column) => ({
      ...column,
      title: t(column.title),
      ...(column.dataIndex === 'phone' && { width: '12%', render: renderPhoneColumn }),
      ...(column.dataIndex === 'level' && { render: renderVipLevelColumn })
    })),
    {
      title: t('common_action'),
      render: (_: unknown, data: PlayerData) => (
        <ActionButtons
          data={data}
          onEdit={handleEdit}
          onEditStatus={openEditStatusModal}
          isEditStatusPending={isEditStatusPending}
          onOpenPermission={() => handleOpenPermission(data)}
        />
      )
    }
  ];

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  useEffect(() => {
    setPage(1);
  }, [searchParams, accountType, setPage]);

  return (
    <>
      <div className="flex justify-between mb-4">
        <RButton type="primary" onClick={() => setOpen(true)}>
          {t('common_add_name', { name: t('pages_player') })}
        </RButton>
        <RTooltip placement="bottomRight" title={t('common_export')} arrow={false} color="gray">
          <span>
            <RButton type="default">
              <DownloadIcon width={16} height={16} />
            </RButton>
          </span>
        </RTooltip>
      </div>
      <RTable
        loading={isPending}
        rowKey="id"
        dataSource={data?.data?.data || []}
        columns={tableColumns}
        pagination={{
          current: page,
          pageSize: limit,
          total: data?.data?.total || 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
      <PlayerModal
        open={open}
        initialValues={initialValues}
        onClose={handleCloseModal}
        onOk={() => {}}
        showAccountDialog={showAccountDialog}
      />
      {AccountDialog}
      <GetPlayerByPhoneModel
        phone={selectedPhone}
        countryCode={selectedPhoneCountryCode}
        open={openGetPlayerByPhone}
        onClose={() => setOpenGetPlayerByPhone(false)}
      />
      <PlayerPermissionModal
        open={openPermission}
        onClose={() => {
          setOpenPermission(false);
          setSelectedRecord(null);
        }}
        id={selectedRecord?.id || 0}
      />
      <VipChangeLogModal
        open={openVipChangeLog}
        onClose={() => {
          setOpenVipChangeLog(false);
          setSelectedRecord(null);
        }}
        id={selectedRecord?.id || 0}
        account={selectedRecord?.account || ''}
      />
    </>
  );
};

const PlayerListPage = () => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useState<SearchFormValues>({});
  const [activeTabKey, setActiveTabKey] = useState('1');

  const handleSearch = (values: SearchFormValues) => {
    setSearchParams(values);
  };

  const handleReset = () => {
    setSearchParams({});
  };

  const handleTabChange = (key: string) => {
    setActiveTabKey(key);
  };

  const tabItems = [
    {
      key: '1',
      label: t('pages_player_accountType_official'),
      children: null
    },
    {
      key: '2',
      label: t('pages_player_accountType_test'),
      children: null
    }
  ];

  const renderTabContent = () => {
    switch (activeTabKey) {
      case '1':
        return <PlayerListTable accountType={1} />;
      case '2':
        return <PlayerListTable accountType={2} />;
      default:
        return null;
    }
  };

  return (
    <SearchParamsContext.Provider value={searchParams}>
      <TableSearchLayout
        searchFieldsContainerClassName="pb-0"
        searchFields={
          <>
            <SearchFormWrap onSearch={handleSearch} onReset={handleReset} />
            <div className="mt-7">
              <RTabs
                size="middle"
                activeKey={activeTabKey}
                onChange={handleTabChange}
                items={tabItems}
                indicator={{ size: 100, align: 'center' }}
                tabBarGutter={40}
                className="custom-tabs"
              />
            </div>
          </>
        }
      >
        <div className="flex-1 min-h-0">{renderTabContent()}</div>
      </TableSearchLayout>
    </SearchParamsContext.Provider>
  );
};

export default PlayerListPage;

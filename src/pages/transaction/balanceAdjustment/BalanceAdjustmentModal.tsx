import { Col, Row } from 'antd';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { type BalanceAdjustmentCreateParams } from '@/api/balanceAdjustment';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RModal from '@/components/RModal';
import RSelect from '@/components/RSelect';
import { BalanceAdjustmentChangeType } from '@/types/balanceAdjustment';

import { useBalanceAdjustmentMutation } from './hooks/useBalanceAdjustment';

export type BalanceAdjustmentFormValue = {
  account: string;
  changeType: BalanceAdjustmentChangeType;
  currency: string;
  amount: number;
  frozenTime?: number;
  note: string;
};

const CURRENCY_OPTIONS = [
  { label: 'pages_transaction_transactionRecord_currency_main', value: 'main' },
  { label: 'pages_transaction_transactionRecord_currency_secondary', value: 'secondary' }
] as const;

export const BalanceAdjustmentModal = ({
  open,
  onOk,
  onClose,
  changeType
}: {
  open: boolean;
  onOk?: () => void;
  onClose: () => void;
  changeType: BalanceAdjustmentChangeType;
}) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<BalanceAdjustmentFormValue>();

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const createBalanceAdjustmentMutation = useBalanceAdjustmentMutation({
    onSuccess: () => {
      onOk?.();
      handleClose();
    }
  });

  const handleSubmit = () => {
    form.submit();
  };

  const onFinish = (values: BalanceAdjustmentFormValue) => {
    const params: BalanceAdjustmentCreateParams = {
      account: values.account,
      changeType: changeType,
      currency: values.currency,
      amount: values.amount,
      note: values.note
    };

    if (changeType === BalanceAdjustmentChangeType.INCREASE && values.frozenTime) {
      params.frozenTime = values.frozenTime;
    }

    createBalanceAdjustmentMutation.mutate(params);
  };

  const translatedCurrencyOptions = useMemo(
    () =>
      CURRENCY_OPTIONS.map((option) => ({
        label: t(option.label),
        value: option.value
      })),
    [t]
  );

  const isIncreaseType = changeType === BalanceAdjustmentChangeType.INCREASE;

  useEffect(() => {
    if (open) {
      form.setFieldsValue({
        currency: 'main',
        changeType: changeType || BalanceAdjustmentChangeType.INCREASE
      });
    }
  }, [open, form, changeType]);

  const modalTitle = isIncreaseType
    ? t('pages_transaction_balanceAdjustment_changeType_increase')
    : t('pages_transaction_balanceAdjustment_changeType_decrease');

  return (
    <RModal
      centered
      maskClosable={false}
      title={modalTitle}
      open={open}
      onCancel={handleClose}
      onOk={handleSubmit}
      loading={createBalanceAdjustmentMutation.isPending}
      width={700}
      destroyOnClose={true}
      forceRender={true}
    >
      <RForm<BalanceAdjustmentFormValue>
        form={form}
        onFinish={onFinish}
        layout="vertical"
        preserve={false}
      >
        <Row>
          <Col span={11}>
            <RForm.Item
              label={t('pages_transaction_balanceAdjustment_account')}
              name="account"
              rules={[
                { required: true, message: t('common_required') },
                {
                  pattern: /^[a-zA-Z0-9]{6,20}$/,
                  message: t('common_account_format_error')
                }
              ]}
            >
              <RInput size="middle" className="h-[32px]" placeholder={t('placeholder_input')} />
            </RForm.Item>
          </Col>

          <Col span={11} offset={1}>
            <RForm.Item
              label={t('pages_transaction_balanceAdjustment_currency')}
              name="currency"
              rules={[{ required: true, message: t('common_required') }]}
              initialValue={'main'}
            >
              <RSelect
                disabled
                options={translatedCurrencyOptions}
                placeholder={t('common_please_select', {
                  name: t('pages_transaction_balanceAdjustment_currency')
                })}
              />
            </RForm.Item>
          </Col>
        </Row>
        <Row>
          <Col span={11}>
            <RForm.Item
              label={t('pages_transaction_balanceAdjustment_amount')}
              name="amount"
              rules={[
                { required: true, message: t('common_required') },
                {
                  min: 0.01,
                  message: t('common_amount_min_error')
                }
              ]}
            >
              <RInput type="number" className='h-[32px]' placeholder={t('placeholder_input')} min={0.01} step={0.01} />
            </RForm.Item>
          </Col>
          {isIncreaseType && (
            <Col span={11} offset={1}>
              <RForm.Item
                label={t('pages_transaction_balanceAdjustment_frozenTime_modal')}
                name="frozenTime"
                rules={[
                  { required: true, message: t('common_required') },
                  {
                    // type: 'number',
                    min: 0,
                    message: t('pages_transaction_balanceAdjustment_frozenTime_min_error')
                  }
                ]}
              >
                <RInput
                  type="number"
                  className="h-[32px]"
                  placeholder={t('placeholder_input')}
                  min={0}
                  step={1}
                />
              </RForm.Item>
            </Col>
          )}
        </Row>

        <Row>
          <Col span={22}>
            <RForm.Item
              label={t('pages_transaction_balanceAdjustment_note')}
              name="note"
              rules={[
                { required: true, message: t('common_required') },
                { max: 500, message: t('common_note_max_length') }
              ]}
            >
              <RInput className="h-[32px]" placeholder={t('placeholder_input')} />
            </RForm.Item>
          </Col>
        </Row>
      </RForm>
    </RModal>
  );
};

export default BalanceAdjustmentModal;

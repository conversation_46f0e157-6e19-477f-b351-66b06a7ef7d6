import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import OperatorCell from '@/components/cells/OperatorCell';
import QuickDateSelect from '@/components/QuickDateSelect';
import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import SearchForm from '@/components/SearchForm';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { BalanceAdjustment, BalanceAdjustmentSearchParams } from '@/types/balanceAdjustment';
import { BalanceAdjustmentChangeType } from '@/types/balanceAdjustment';
import { numberFormat } from '@/utils/numbers';
import { cleanSearchParams } from '@/utils/object';

import BalanceAdjustmentModal from './BalanceAdjustmentModal';
import { useBalanceAdjustment } from './hooks/useBalanceAdjustment';

type SearchFormValues = {
  changeType?: number;
  excludeTest?: number;
  conditionType?: (typeof CONDITION_OPTIONS)[number]['value'];
  conditionValue?: string;
  start: number;
  end: number;
  currency?: string;
};

const CONDITION_OPTIONS = [
  { label: 'pages_transaction_balanceAdjustment_account', value: 'account' },
  { label: 'pages_transaction_balanceAdjustment_createdBy', value: 'createdBy' }
] as const;

const CHANGE_TYPE_OPTIONS = [
  { label: 'common_all', value: 'all' },
  { label: 'pages_transaction_balanceAdjustment_changeType_increase', value: 1 },
  { label: 'pages_transaction_balanceAdjustment_changeType_decrease', value: 2 }
] as const;

const CURRENCY_OPTIONS = [
  { label: 'pages_transaction_transactionRecord_currency_main', value: 'main' },
  { label: 'pages_transaction_transactionRecord_currency_secondary', value: 'secondary' }
] as const;

const defaultToday = [dayjs().startOf('day').valueOf(), dayjs().endOf('day').valueOf()] as [
  number,
  number
];

const getTableColumns = (t: (key: string) => string) => [
  {
    title: t('pages_transaction_balanceAdjustment_orderTime'),
    dataIndex: 'createdAt',
    render: (createdAt: number, record: BalanceAdjustment) => (
      <OperatorCell record={{ updatedBy: record.id, updatedAt: createdAt }} />
    )
  },
  {
    title: t('pages_transaction_balanceAdjustment_targetAccount'),
    dataIndex: 'account'
  },
  {
    title: t('pages_transaction_balanceAdjustment_changeType'),
    dataIndex: 'changeType',
    render: (changeType: number) => {
      const option = CHANGE_TYPE_OPTIONS.find((option) => option.value === changeType);
      return option ? t(option.label) : changeType;
    }
  },
  {
    title: t('pages_transaction_balanceAdjustment_currency'),
    dataIndex: 'currency',
    render: (currency: string) => {
      const option = CURRENCY_OPTIONS.find((option) => option.value === currency);
      return option ? t(option.label) : currency;
    }
  },

  {
    title: t('pages_transaction_balanceAdjustment_amount'),
    dataIndex: 'amount',
    render: (amount: number) => {
      return (
        <span className={amount > 0 ? 'text-success' : 'text-warning'}>{numberFormat(amount)}</span>
      );
    }
  },
  {
    title: t('pages_transaction_balanceAdjustment_remain'),
    dataIndex: 'remain',
    render: (remain: number) => numberFormat(remain)
  },
  {
    title: t('pages_transaction_balanceAdjustment_createdBy'),
    dataIndex: 'createdBy'
  },
  {
    title: t('pages_transaction_balanceAdjustment_note'),
    dataIndex: 'note',
    render: (note: string) => {
      return <div className="whitespace-pre-wrap break-words">{note}</div>;
    }
  }
];

const ConditionSearch = () => {
  const { t } = useTranslation();

  const translatedOptions = useMemo(
    () =>
      CONDITION_OPTIONS.map((option) => ({
        label: t(option.label),
        value: option.value
      })),
    [t]
  );

  return (
    <RForm.Item label={t('common_condition')}>
      <RForm.Item name="conditionType" className="inline-block !mr-2" initialValue="account">
        <RSelect options={translatedOptions} />
      </RForm.Item>
      <RForm.Item name="conditionValue" className="inline-block">
        <RInput className="max-h-[32px]" />
      </RForm.Item>
    </RForm.Item>
  );
};

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: Omit<BalanceAdjustmentSearchParams, 'page' | 'limit'>) => void;
  onReset: () => void;
}) => {
  const { t } = useTranslation();

  const handleSearch = (values: SearchFormValues) => {
    const { conditionType, conditionValue, start, end, ...rest } = values;
    const searchValues = {
      currency: 'main',
      ...cleanSearchParams(rest),
      timeStart: start,
      timeEnd: end
    };

    if (conditionType && conditionValue) {
      onSearch({
        ...searchValues,
        [conditionType]: conditionValue
      });
    } else {
      onSearch(searchValues);
    }
  };

  const translatedChangeTypeOptions = useMemo(
    () =>
      CHANGE_TYPE_OPTIONS.map((option) => ({
        label: t(option.label),
        value: option.value
      })),
    [t]
  );

  return (
    <SearchForm<SearchFormValues> onSearch={handleSearch} onReset={onReset} className="">
      <RForm.Item name="date" label={t('common_timeSelect')} initialValue={defaultToday}>
        <QuickDateSelect defaultActiveKey="today" />
      </RForm.Item>
      <ConditionSearch />
      <RForm.Item name="changeType" label={t('pages_transaction_balanceAdjustment_changeType')}>
        <RSelect
          options={translatedChangeTypeOptions}
          placeholder={t('common_please_select', {
            name: t('pages_transaction_balanceAdjustment_changeType')
          })}
        />
      </RForm.Item>
      <RForm.Item name="excludeTest" label={t('common_filtertest')} initialValue={1}>
        <RSelect
          options={[
            { label: t('common_yes'), value: 1 }, // 排除測試帳號
            { label: t('common_no'), value: 0 } // 包含全部
          ]}
        />
      </RForm.Item>
    </SearchForm>
  );
};

const BalanceAdjustmentPage = () => {
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [params, setParams] = useState<Omit<BalanceAdjustmentSearchParams, 'page' | 'limit'>>({
    timeStart: defaultToday[0],
    timeEnd: defaultToday[1],
    excludeTest: 1,
    currency: 'main'
  });
  const [modalChangeType, setModalChangeType] = useState<BalanceAdjustmentChangeType | null>(null);

  const tableColumns = useMemo(() => getTableColumns(t), [t]);

  const { data, isPending } = useBalanceAdjustment({
    page,
    limit,
    ...params
  });

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  const handleSearch = (newParams: Omit<BalanceAdjustmentSearchParams, 'page' | 'limit'>) => {
    setParams(newParams);
    setPage(1);
  };

  const handleReset = () => {
    const resetParams = {
      timeStart: defaultToday[0],
      timeEnd: defaultToday[1],
      excludeTest: 1,
      currency: 'main'
    };
    setParams(resetParams);
    setPage(1);
  };

  const handleOpenModal = (changeType: BalanceAdjustmentChangeType) => {
    setModalChangeType(changeType);
  };

  const handleCloseModal = () => {
    setModalChangeType(null);
  };

  const handleModalSuccess = () => {
    handleCloseModal();
    handleReset();
    setPage(1);
  };

  return (
    <>
      <TableSearchLayout
        searchFields={<SearchFormWrap onSearch={handleSearch} onReset={handleReset} />}
      >
        <div>
          <div className="flex gap-2 mb-4">
            <RButton
              type="primary"
              onClick={() => handleOpenModal(BalanceAdjustmentChangeType.INCREASE)}
            >
              {t('pages_transaction_balanceAdjustment_changeType_increase')}
            </RButton>
            <RButton
              // type="danger"
              color="danger"
              onClick={() => handleOpenModal(BalanceAdjustmentChangeType.DECREASE)}
            >
              {t('pages_transaction_balanceAdjustment_changeType_decrease')}
            </RButton>
          </div>

          <RTable
            loading={isPending}
            rowKey="id"
            dataSource={data?.data || []}
            columns={tableColumns}
            pagination={{
              current: page,
              pageSize: limit,
              total: data?.total || 0,
              showSizeChanger: true,
              onChange: handleChangePage
            }}
          />
        </div>
      </TableSearchLayout>

      {modalChangeType && (
        <BalanceAdjustmentModal
          open={!!modalChangeType}
          onClose={handleCloseModal}
          onOk={handleModalSuccess}
          changeType={modalChangeType}
        />
      )}
    </>
  );
};

export default BalanceAdjustmentPage;

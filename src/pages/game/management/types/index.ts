import { Game, GameCategory, GameMaintenance, GameProvider } from "@/types/game";

// Union type for all game management data types
export type GameManagementData = GameProvider | GameCategory | Game | GameMaintenance;

// Tab types
export type GameManagementTab = 'providers' | 'categories' | 'games' | 'maintenance';

// Base interface for pagination props
export interface PaginationProps {
  page: number;
  setPage: (page: number) => void;
  limit: number;
  setLimit: (limit: number) => void;
  handleChangePage: (page: number, pageSize: number) => void;
}

// Base interface for search parameters
export interface BaseSearchParams {
  status?: number;
  [key: string]: string | number | boolean | undefined;
}

// Specific search params for each tab
export interface ProviderSearchParams extends BaseSearchParams {
  providerName?: string;
  gameName?: string;
}

export interface CategorySearchParams extends BaseSearchParams {
  categoryName?: string;
}

export interface GameSearchParams extends BaseSearchParams {
  providerId?: string;
  categoryId?: string;
  noIcon: number;
  name?: string;
  page?: number;
  limit?: number;
}

export interface MaintenanceSearchParams extends BaseSearchParams {
  providerName?: string;
  gameName?: string;
}

// Union type for all search params
export type GameManagementSearchParams =
  | ProviderSearchParams
  | CategorySearchParams
  | GameSearchParams
  | MaintenanceSearchParams;

// Base interface for tab component props
export interface BaseTabProps {
  searchParams: BaseSearchParams;
  onSearch: (params: BaseSearchParams) => void;
  onReset: () => void;
}

// Interface for table data response
export interface TableDataResponse<T> {
  data: T[];
  total: number;
  loading: boolean;
  pagination: PaginationProps;
}

// Interface for mutation handlers
export interface MutationHandlers<T = unknown> {
  onEditStatus: (id: number | string, currentStatus: number) => void;
  onEdit?: (record: T) => void;
  onDelete?: (record: T) => void;
}

// Interface for table column configuration
export interface TableColumnConfig {
  dataIndex: string;
  translationKey: string;
  render?: (value: unknown, record: Record<string, unknown>) => React.ReactNode;
  width?: string | number;
}

// Interface for action button configuration
export interface ActionButtonConfig<T = unknown> {
  buttons: ('edit' | 'status' | 'delete')[];
  handlers: MutationHandlers<T>;
  loadingStates: {
    isEditStatusPending?: boolean;
    isEditPending?: boolean;
    isDeletePending?: boolean;
  };
}

// Interface for game management query configuration
export interface GameManagementQueryConfig<T, P> {
  queryKey: string[];
  queryFn: (params: P) => Promise<unknown>;
  enabled?: boolean;
  select?: (data: unknown) => T[];
}

// Interface for game management mutation configuration
export interface GameManagementMutationConfig {
  mutationFn: (params: Record<string, unknown>) => Promise<unknown>;
  invalidateQueries: string[];
  successMessage?: string;
  errorMessage?: string;
}

// Interface for tab configuration
export interface TabConfig<T, P> {
  key: GameManagementTab;
  queryConfig: GameManagementQueryConfig<T, P>;
  mutationConfig: GameManagementMutationConfig;
  columns: TableColumnConfig[];
  searchFields: string[];
  hasAddButton?: boolean;
  addButtonHandler?: () => void;
}

// Generic interface for game management hook return
export interface UseGameManagementReturn<T> {
  data: T[];
  total: number;
  loading: boolean;
  pagination: PaginationProps;
  searchParams: BaseSearchParams;
  handleSearch: (params: BaseSearchParams) => void;
  handleReset: () => void;
  mutationHandlers: MutationHandlers;
  loadingStates: {
    isEditStatusPending: boolean;
    isEditPending?: boolean;
    isDeletePending?: boolean;
  };
}

// Interface for search form field configuration
export interface SearchFieldConfig {
  name: string;
  type: 'input' | 'select';
  translationKey: string;
  defaultValue?: string | number;
  options?: { label: string; value: string | number }[];
  placeholder?: string;
  span?: number;
  isLoading?: boolean;
}

// Interface for search form props
export interface SearchFormProps {
  fields: SearchFieldConfig[];
  onSearch: (values: Record<string, string | number | boolean | undefined>) => void;
  onReset: () => void;
}

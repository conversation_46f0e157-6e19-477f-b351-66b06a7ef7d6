import { Divider, Toolt<PERSON> } from 'antd';
import type { TableRowSelection } from 'antd/es/table/interface';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RModal from '@/components/RModal';
import RSelect from '@/components/RSelect';
import RTable, { RTableColumnsType } from '@/components/RTable';
import {
  useGameCategoryQuery,
  useGameList,
  useGameProviderQuery
} from '@/pages/game/management/hooks';
import { Game } from '@/types/game';

export interface GameSelectionModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (selectedGames: Game[]) => void;
  title?: string;
  multiSelect?: boolean;
  selectedGameIds?: string[];
  disabledGameIds?: string[];
  maxItems?: number;
  preselectCategoryId?: string;
}

export interface GameSelectionSearchParams {
  name?: string;
  providerId?: string;
  categoryId?: string;
  status?: number;
}

const GameSelectionModal = ({
  open,
  onClose,
  onConfirm,
  title,
  multiSelect = true,
  selectedGameIds,
  disabledGameIds,
  maxItems,
  preselectCategoryId
}: GameSelectionModalProps) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<GameSelectionSearchParams>();
  const [searchParams, setSearchParams] = useState<GameSelectionSearchParams>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  // Initialize selected keys including disabled games
  useEffect(() => {
    const allSelectedKeys = [...(selectedGameIds || []), ...(disabledGameIds || [])];
    setSelectedRowKeys(allSelectedKeys);
  }, [selectedGameIds, disabledGameIds]);

  useEffect(() => {
    if (preselectCategoryId) {
      form.setFieldsValue({ categoryId: preselectCategoryId });
    }
  }, [preselectCategoryId, form]);

  const enableSearch = useMemo(() => {
    const objWillCheck = { ...searchParams };
    // remove the default category id to make sure the search is enabled
    if (preselectCategoryId) {
      delete objWillCheck.categoryId;
    }
    return Object.values(objWillCheck).some((value) => value !== undefined && value !== '');
  }, [preselectCategoryId, searchParams]);

  // Fetch all games without pagination
  const gameListQuery = useGameList({
    searchParams: {
      ...searchParams,
      noIcon: 0
    },
    enabled: enableSearch
  });

  const gameList = useMemo<Game[]>(() => {
    if (!enableSearch) return [] as Game[];
    return (gameListQuery.data || []) as Game[];
  }, [gameListQuery.data, enableSearch]);

  // Fetch providers and categories for filters
  const { data: providers = [], isPending: isProvidersLoading } = useGameProviderQuery();
  const { data: categories = [], isPending: isCategoriesLoading } = useGameCategoryQuery();

  // Provider options
  const providerOptions = useMemo(
    () => [
      { label: t('common_all'), value: 'all' },
      ...providers.map((provider) => ({
        label: provider.name,
        value: provider.id
      }))
    ],
    [providers, t]
  );

  // Category options
  const categoryOptions = useMemo(
    () => [
      { label: t('common_all'), value: 'all' },
      ...categories.map((category) => ({
        label: category.name,
        value: category.id
      }))
    ],
    [categories, t]
  );

  // Handle search
  const handleSearch = (values: GameSelectionSearchParams) => {
    setSearchParams(values);
  };

  // Handle reset
  const handleReset = () => {
    form.resetFields();
    const resetParams: GameSelectionSearchParams = {};
    if (preselectCategoryId) {
      resetParams.categoryId = preselectCategoryId;
      form.setFieldsValue({ categoryId: preselectCategoryId });
    }
    setSearchParams(resetParams);
  };

  // Handle confirm
  const handleConfirm = () => {
    // Get all selected games including disabled ones
    const allSelectedGames = gameList.filter((game) => selectedRowKeys.includes(game.id));
    // Filter out disabled games
    const finalSelectedGames = allSelectedGames.filter(
      (game) => !disabledGameIds?.includes(game.id)
    );
    onConfirm(finalSelectedGames);
    handleClose();
  };

  // Handle close
  const handleClose = () => {
    const allSelectedKeys = [...(selectedGameIds || []), ...(disabledGameIds || [])];
    setSelectedRowKeys(allSelectedKeys);
    setSearchParams({});
    form.resetFields();
    onClose();
  };

  // Helper function to check if selection exceeds maxItems
  const isMaxItemsReached = (newSelectedKeys: React.Key[]) => {
    if (!maxItems || !multiSelect) return false;
    return newSelectedKeys.length > maxItems;
  };

  // Helper function to get selectable count (excluding disabled games)
  const getSelectableCount = (keys: string[]) => {
    return keys.filter((key) => !disabledGameIds?.includes(key)).length;
  };

  // Row selection configuration
  const rowSelection: TableRowSelection<Game> = {
    type: multiSelect ? 'checkbox' : 'radio',
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[]) => {
      // Always include disabled games in selection
      const enabledSelectedKeys = selectedKeys.filter(
        (key) => !disabledGameIds?.includes(key as string)
      );
      const finalSelectedKeys = [...enabledSelectedKeys, ...(disabledGameIds || [])];

      // Check maxItems validation for multi-select
      if (maxItems && multiSelect && isMaxItemsReached(finalSelectedKeys)) {
        // Don't update if it would exceed maxItems
        return;
      }

      setSelectedRowKeys(finalSelectedKeys as string[]);
    },
    getCheckboxProps: (record: Game) => {
      const isDisabled = disabledGameIds?.includes(record.id);
      const isSelected = selectedRowKeys.includes(record.id);

      // Disable if:
      // 1. Game is in disabledGameIds, OR
      // 2. MaxItems reached and game is not already selected (for multi-select)
      const isMaxReached =
        maxItems && multiSelect && getSelectableCount(selectedRowKeys) >= maxItems && !isSelected;

      return {
        disabled: Boolean(isDisabled || isMaxReached),
        name: record.name
      };
    },
    onSelectAll: (selected: boolean) => {
      if (selected) {
        // Select all non-disabled games plus keep disabled games
        let allSelectableKeys = gameList
          .filter((game) => !disabledGameIds?.includes(game.id))
          .map((game) => game.id);

        // Limit selection if maxItems is set
        if (maxItems && multiSelect) {
          const currentDisabledCount = disabledGameIds?.length || 0;
          const maxSelectableCount = maxItems - currentDisabledCount;
          allSelectableKeys = allSelectableKeys.slice(0, Math.max(0, maxSelectableCount));
        }

        const finalKeys = [...allSelectableKeys, ...(disabledGameIds || [])];
        setSelectedRowKeys(finalKeys);
      } else {
        // Deselect all except disabled games
        setSelectedRowKeys([...(disabledGameIds || [])]);
      }
    }
  };

  // Table columns (selection handled by rowSelection prop)
  const columns: RTableColumnsType<Game> = [
    {
      title: t('game_layout_table_image'),
      dataIndex: 'icon',
      key: 'icon',
      width: 80,
      render: (icon: string) => (
        <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
          {icon ? (
            <img src={icon} alt="game" className="w-full h-full object-cover rounded" />
          ) : (
            <span className="text-gray-400 text-xs">IMG</span>
          )}
        </div>
      )
    },
    {
      title: t('game_layout_table_name'),
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Game) => {
        const isDisabled = disabledGameIds?.includes(record.id);
        if (isDisabled) {
          return (
            <Tooltip title={t('game_selection_disabled_tooltip')}>
              <span className="text-gray-500">{name}</span>
            </Tooltip>
          );
        }
        return name;
      }
    },
    {
      title: t('game_layout_table_provider'),
      dataIndex: 'provider',
      key: 'provider',
      render: (providerId: string) => {
        const provider = providers.find((p) => p.id === providerId);
        return provider?.name || providerId;
      }
    },
    {
      title: t('game_layout_table_category'),
      dataIndex: 'category',
      key: 'category',
      render: (categoryId: string) => {
        const category = categories.find((c) => c.id === categoryId);
        return category?.name || categoryId;
      }
    }
  ];

  return (
    <RModal
      title={title || t('game_selection_modal_title')}
      open={open}
      onCancel={handleClose}
      onOk={handleConfirm}
      width={800}
      okButtonProps={{
        text: t('common_confirm'),
        disabled: selectedRowKeys.length === 0,
        show: true
      }}
      cancelButtonProps={{ text: t('common_cancel'), show: true }}
      destroyOnClose={true}
    >
      <div className="space-y-4">
        {/* Search Form */}
        <RForm form={form} layout="inline" onFinish={handleSearch} className="gap-y-2">
          <RForm.Item name="providerId" label={t('select_game_provider')}>
            <RSelect
              allowClear
              placeholder={t('common_please_select', { name: t('select_game_provider') })}
              options={providerOptions}
              loading={isProvidersLoading}
              style={{ minWidth: 200 }}
            />
          </RForm.Item>

          <RForm.Item name="categoryId" label={t('select_game_category')}>
            <RSelect
              allowClear
              placeholder={t('common_please_select', { name: t('select_game_category') })}
              options={categoryOptions}
              loading={isCategoriesLoading}
              style={{ minWidth: 200 }}
              disabled={!!preselectCategoryId}
            />
          </RForm.Item>
          <div className="basis-full" />

          <RForm.Item name="name" label={t('select_game_name')}>
            <RInput
              allowClear
              placeholder={t('common_please_enter', { name: t('select_game_name') })}
              style={{ minWidth: 200 }}
            />
          </RForm.Item>

          <RForm.Item>
            <div className="flex gap-2">
              <RButton type="primary" htmlType="submit">
                {t('common_search')}
              </RButton>
              <RButton type="default" onClick={handleReset}>
                {t('common_reset')}
              </RButton>
            </div>
          </RForm.Item>
        </RForm>
        <Divider />

        {/* Selection Summary */}
        <div className="text-sm text-gray-600">
          {maxItems && multiSelect ? (
            <span
              dangerouslySetInnerHTML={{
                __html:
                  t('game_selection_summary_with_max', {
                    selected: selectedRowKeys.length,
                    max: maxItems,
                    total: gameList.length
                  }) +
                  (selectedRowKeys.length >= maxItems
                    ? `<span className="text-orange-600 ml-2">
                  ${t('game_selection_max_reached')}
                </span>`
                    : '')
              }}
            ></span>
          ) : (
            t('game_selection_summary', {
              selected: selectedRowKeys.length,
              total: gameList.length
            })
          )}
        </div>

        {/* Games Table */}
        <RTable
          rowKey="id"
          dataSource={gameList}
          columns={columns}
          loading={gameListQuery.isLoading}
          pagination={false}
          scroll={{ y: 400 }}
          size="small"
          rowSelection={rowSelection}
          rowClassName={(record) =>
            disabledGameIds?.includes(record.id) ? 'disabled-game-row' : ''
          }
        />
      </div>
    </RModal>
  );
};

export default GameSelectionModal;
